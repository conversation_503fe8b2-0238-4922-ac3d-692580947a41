export interface User {
  id: number;
  name: string;
  email?: string;
  phone_number?: string;
  status: number;
  is_email_verified: boolean;
  is_phone_verified: boolean;
  tujuan_pembayaran?: any[];
  city?: string;
  country?: string;
  created_at: string;
  updated_at: string;
}

export interface SocialMedia {
  id: number;
  user_id: number;
  platform: 'ig' | 'fb' | 'x' | 'thread' | 'youtube' | 'tiktok';
  username_or_url: string;
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  token?: string;
  errors?: Record<string, string[]>;
}

export interface LoginRequest {
  email?: string;
  phone_number?: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email?: string;
  phone_number?: string;
  password: string;
}

export interface OtpRequest {
  phone_number: string;
  type: 'register' | 'login' | 'reset_password';
}

export interface OtpVerifyRequest {
  phone_number: string;
  otp_code: string;
  type: 'register' | 'login' | 'reset_password';
}

export interface UpdateProfileRequest {
  name?: string;
  email?: string;
  phone_number?: string;
  city?: string;
  country?: string;
}

export interface UpdatePaymentRequest {
  tujuan_pembayaran: any[];
}

export interface UpdateSocialMediaRequest {
  social_media: {
    platform: 'ig' | 'fb' | 'x' | 'thread' | 'youtube' | 'tiktok';
    username_or_url: string;
  }[];
} 