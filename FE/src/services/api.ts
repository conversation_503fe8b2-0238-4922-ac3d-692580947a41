import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse } from '@/types';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor untuk menambahkan token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor untuk handling error
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async register(data: any): Promise<ApiResponse> {
    const response = await this.api.post('/register', data);
    return response.data;
  }

  async login(data: any): Promise<ApiResponse> {
    const response = await this.api.post('/login', data);
    return response.data;
  }

  async logout(): Promise<ApiResponse> {
    const response = await this.api.post('/logout');
    return response.data;
  }

  async requestOtp(data: any): Promise<ApiResponse> {
    const response = await this.api.post('/request-otp', data);
    return response.data;
  }

  async verifyOtp(data: any): Promise<ApiResponse> {
    const response = await this.api.post('/verify-otp', data);
    return response.data;
  }

  async verifyEmail(token: string): Promise<ApiResponse> {
    const response = await this.api.post('/email/verify', { token });
    return response.data;
  }

  async resendEmail(): Promise<ApiResponse> {
    const response = await this.api.post('/email/resend');
    return response.data;
  }

  async forgotPassword(data: any): Promise<ApiResponse> {
    const response = await this.api.post('/forgot-password', data);
    return response.data;
  }

  async resetPassword(data: any): Promise<ApiResponse> {
    const response = await this.api.post('/reset-password', data);
    return response.data;
  }

  // User endpoints
  async getProfile(): Promise<ApiResponse> {
    const response = await this.api.get('/user');
    return response.data;
  }

  async updateProfile(data: any): Promise<ApiResponse> {
    const response = await this.api.put('/user', data);
    return response.data;
  }

  async deleteAccount(): Promise<ApiResponse> {
    const response = await this.api.delete('/user');
    return response.data;
  }

  async updatePayment(data: any): Promise<ApiResponse> {
    const response = await this.api.put('/user/payment', data);
    return response.data;
  }

  async updateSocialMedia(data: any): Promise<ApiResponse> {
    const response = await this.api.put('/user/social-media', data);
    return response.data;
  }
}

export const apiService = new ApiService(); 